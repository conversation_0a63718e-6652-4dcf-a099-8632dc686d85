# xtend

[![browser support][3]][4]

[![locked](http://badges.github.io/stability-badges/dist/locked.svg)](http://github.com/badges/stability-badges)

Extend like a boss

xtend is a basic utility library which allows you to extend an object by appending all of the properties from each object in a list. When there are identical properties, the right-most property takes precedence.

## Examples

```js
var extend = require("xtend")

// extend returns a new object. Does not mutate arguments
var combination = extend({
    a: "a",
    b: "c"
}, {
    b: "b"
})
// { a: "a", b: "b" }
```

## Stability status: Locked

## MIT Licensed 


  [3]: http://ci.testling.com/Raynos/xtend.png
  [4]: http://ci.testling.com/Raynos/xtend
