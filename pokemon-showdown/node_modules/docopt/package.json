{"name": "docopt", "version": "0.6.2", "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "a command line option parser that will make you smile", "main": "docopt.js", "scripts": {"test": "mocha --compilers coffee:coffee-script/register", "lint": "coffeelint docopt.coffee test/*.coffee", "prepublish": "coffee -c docopt.coffee"}, "engines": {"node": ">=0.10.0"}, "keywords": ["command", "options", "argument", "args", "cli", "commandline"], "licenses": "MIT", "repository": {"type": "git", "url": "git://github.com/scarnie/docopt.coffee.git"}, "devDependencies": {"chai": "^2.2.0", "coffee-script": "^1.9.1", "coffeelint": "^1.9.2", "mocha": "^2.2.1"}}