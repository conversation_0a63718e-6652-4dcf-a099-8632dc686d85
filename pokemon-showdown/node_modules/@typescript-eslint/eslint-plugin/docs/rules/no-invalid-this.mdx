---
description: 'Disallow `this` keywords outside of classes or class-like objects.'
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/no-invalid-this** for documentation.

import TypeScriptOverlap from '@site/src/components/TypeScriptOverlap';

<TypeScriptOverlap strict />

It adds support for TypeScript's `this` parameters.
