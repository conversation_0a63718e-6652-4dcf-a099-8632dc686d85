---
description: 'Require using Error objects as Promise rejection reasons.'
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/prefer-promise-reject-errors** for documentation.

It uses type information to enforce that `Promise`s are only rejected with `Error` objects.

## Examples

<Tabs>
<TabItem value="❌ Incorrect">

```ts
Promise.reject('error');

const err = new Error();
Promise.reject('an ' + err);

new Promise((resolve, reject) => reject('error'));

new Promise((resolve, reject) => {
  const err = new Error();
  reject('an ' + err);
});
```

</TabItem>
<TabItem value="✅ Correct">

```ts
Promise.reject(new Error());

class CustomError extends Error {
  // ...
}
Promise.reject(new CustomError());

new Promise((resolve, reject) => reject(new Error()));

new Promise((resolve, reject) => {
  class CustomError extends Error {
    // ...
  }
  return reject(new CustomError());
});
```

</TabItem>
</Tabs>

## Options

This rule adds the following options:

```ts
interface Options {
  /**
   * Whether to always allow throwing values typed as `any`.
   */
  allowThrowingAny?: boolean;

  /**
   * Whether to always allow throwing values typed as `unknown`.
   */
  allowThrowingUnknown?: boolean;
}

const defaultOptions: Options = {
  allowThrowingAny: false,
  allowThrowingUnknown: false,
};
```
