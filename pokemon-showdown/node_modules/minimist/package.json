{"name": "minimist", "version": "0.0.10", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT"}