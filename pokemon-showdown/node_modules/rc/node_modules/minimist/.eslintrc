{
  "root": true,

  "extends": "@ljharb/eslint-config/node/0.4",

  "rules": {
    "array-bracket-spacing": 0,
    "array-element-newline": 0,
    "brace-style": 1,
    "camelcase": 1,
    "comma-dangle": 1,
    "comma-spacing": 1,
    "complexity": 0,
    "curly": 1,
    "dot-notation": 1,
    "eol-last": 1,
    "func-style": 1,
    "function-paren-newline": 1,
    "indent": [1, 4],
    "key-spacing": 1,
    "max-lines-per-function": 0,
    "max-nested-callbacks": 1,
    "max-statements": 0,
    "multiline-comment-style": 1,
    "no-array-constructor": 1,
    "no-continue": 1,
    "no-div-regex": 1,
    "no-extra-parens": 1,
    "no-mixed-operators": 1,
    "no-multi-spaces": 1,
    "no-multiple-empty-lines": 1,
    "no-param-reassign": 1,
    "no-plusplus": 1,
    "no-proto": 1,
    "no-redeclare": 1,
    "no-restricted-syntax": 1,
    "no-shadow": 1,
    "no-trailing-spaces": 1,
    "no-unused-vars": 1,
    "no-use-before-define": 1,
    "object-curly-newline": 1,
    "object-curly-spacing": 1,
    "operator-linebreak": 1,
    "quote-props": 1,
    "quotes": 1,
    "semi-style": 1,
    "semi": 1,
    "space-before-blocks": 1,
    "space-before-function-paren": 1,
    "space-infix-ops": 1,
    "strict": 1,
    "wrap-regex": 1,
  },
}
