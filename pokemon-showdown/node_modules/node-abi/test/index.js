var test = require('tape')
var semver = require('semver')
var getAbi = require('../index').getAbi
var getTarget = require('../index').getTarget
var getNextTarget = require('../index')._getNextTarget
var allTargets = require('../index').allTargets

test('getNextTarget gets the next unsupported target', function (t) {
  var mockTargets = [
    {runtime: 'node', target: '7.0.0', abi: '51', lts: false},
    {runtime: 'node', target: '8.0.0', abi: '57', lts: false},
    {runtime: 'electron', target: '0.36.0', abi: '47', lts: false},
    {runtime: 'electron', target: '1.1.0', abi: '48', lts: false}
  ]
  t.equal(getNextTarget('node', mockTargets), '9.0.0')
  t.equal(getNextTarget('electron', mockTargets), '1.2.0')
  t.end()
})

test('getTarget calculates correct Node target', function (t) {
  t.equal(getTarget(undefined), process.versions.node)
  t.equal(getTarget(null), process.versions.node)
  t.equal(getTarget('11'), '0.10.4')
  t.equal(getTarget('14'), '0.11.11')
  t.equal(getTarget('46'), '4.0.0')
  t.equal(getTarget('47'), '5.0.0')
  t.equal(getTarget('48'), '6.0.0')
  t.equal(getTarget('51'), '7.0.0')
  t.equal(getTarget('67'), '11.0.0')
  t.equal(getTarget('72'), '12.0.0')
  t.equal(getTarget('83'), '14.0.0')
  t.equal(getTarget('88'), '15.0.0')
  t.end()
})

test('getTarget calculates correct Electron target', function (t) {
  t.throws(getTarget.bind(null, '14', 'electron'))
  t.equal(getTarget('47', 'electron'), '0.36.0')
  t.equal(getTarget('48', 'electron'), '1.1.0')
  t.equal(getTarget('49', 'electron'), '1.3.0')
  t.equal(getTarget('50', 'electron'), '1.4.0')
  t.equal(getTarget('76', 'electron'), '8.0.0')
  t.equal(getTarget('82', 'electron'), '10.0.0')
  t.equal(getTarget('89', 'electron'), '13.0.0')
  t.end()
})

test('getTarget calculates correct Node-Webkit target', function (t) {
  t.throws(getTarget.bind(null, '14', 'ode-webkit'))
  t.equal(getTarget('47', 'node-webkit'), '0.13.0')
  t.equal(getTarget('48', 'node-webkit'), '0.15.0')
  t.equal(getTarget('51', 'node-webkit'), '0.18.3')
  t.equal(getTarget('57', 'node-webkit'), '0.23.0')
  t.equal(getTarget('59', 'node-webkit'), '0.26.5')
  t.end()
})

test('getAbi calculates correct Node ABI', function (t) {
  t.equal(getAbi(undefined), process.versions.modules)
  t.equal(getAbi(null), process.versions.modules)
  t.throws(function () { getAbi('a.b.c') })
  t.throws(function () { getAbi(getNextTarget('node')) })
  t.equal(getAbi('15.0.0'), '88')
  t.equal(getAbi('14.0.0'), '83')
  t.equal(getAbi('13.0.0'), '79')
  t.equal(getAbi('12.0.0'), '72')
  t.equal(getAbi('11.0.0'), '67')
  t.equal(getAbi('7.2.0'), '51')
  t.equal(getAbi('7.0.0'), '51')
  t.equal(getAbi('6.9.9'), '48')
  t.equal(getAbi('6.0.0'), '48')
  t.equal(getAbi('5.9.9'), '47')
  t.equal(getAbi('5.0.0'), '47')
  t.equal(getAbi('4.9.9'), '46')
  t.equal(getAbi('4.0.0'), '46')
  t.equal(getAbi('0.12.17'), '14')
  t.equal(getAbi('0.12.0'), '14')
  t.equal(getAbi('0.11.16'), '14')
  t.equal(getAbi('0.11.11'), '14')
  t.equal(getAbi('0.11.10'), '13')
  t.equal(getAbi('0.11.8'), '13')
  t.equal(getAbi('0.11.7'), '0x000C')
  t.equal(getAbi('0.11.0'), '0x000C')
  t.equal(getAbi('0.10.48'), '11')
  t.equal(getAbi('0.10.30'), '11')
  t.equal(getAbi('0.10.4'), '11')
  t.equal(getAbi('0.10.3'), '0x000B')
  t.equal(getAbi('0.10.1'), '0x000B')
  t.equal(getAbi('0.10.0'), '0x000B')
  t.equal(getAbi('0.9.12'), '0x000B')
  t.equal(getAbi('0.9.9'), '0x000B')
  t.equal(getAbi('0.9.8'), '0x000A')
  t.equal(getAbi('0.9.1'), '0x000A')
  t.equal(getAbi('0.9.0'), '1')
  t.equal(getAbi('0.8.0'), '1')
  t.equal(getAbi('0.2.0'), '1')
  t.end()
})

test('getAbi calculates correct Electron ABI', function (t) {
  t.throws(function () { getAbi(undefined, 'electron') })
  t.throws(function () { getAbi(getNextTarget('electron'), 'electron') })
  t.equal(getAbi('15.0.0-beta.1', 'electron'), '89')
  t.equal(getAbi('14.1.1', 'electron'), '97')
  t.equal(getAbi('14.0.0', 'electron'), '89')
  t.equal(getAbi('13.0.0', 'electron'), '89')
  t.equal(getAbi('12.0.0', 'electron'), '87')
  t.equal(getAbi('11.0.0', 'electron'), '85')
  t.equal(getAbi('10.0.0', 'electron'), '82')
  t.equal(getAbi('9.0.0', 'electron'), '80')
  t.equal(getAbi('8.0.0', 'electron'), '76')
  t.equal(getAbi('7.0.0', 'electron'), '75')
  t.equal(getAbi('6.0.0', 'electron'), '73')
  t.equal(getAbi('5.0.0', 'electron'), '70')
  t.equal(getAbi('4.1.4', 'electron'), '69')
  t.equal(getAbi('4.0.4', 'electron'), '69')
  t.equal(getAbi('4.0.3', 'electron'), '64')
  t.equal(getAbi('3.1.8', 'electron'), '64')
  t.equal(getAbi('2.0.18', 'electron'), '57')
  t.equal(getAbi('1.4.0', 'electron'), '50')
  t.equal(getAbi('1.3.0', 'electron'), '49')
  t.equal(getAbi('1.2.0', 'electron'), '48')
  t.equal(getAbi('1.1.0', 'electron'), '48')
  t.equal(getAbi('1.0.0', 'electron'), '47')
  t.equal(getAbi('0.37.0', 'electron'), '47')
  t.equal(getAbi('0.36.0', 'electron'), '47')
  t.equal(getAbi('0.35.0', 'electron'), '46')
  t.equal(getAbi('0.34.0', 'electron'), '46')
  t.equal(getAbi('0.33.0', 'electron'), '46')
  t.equal(getAbi('0.32.0', 'electron'), '45')
  t.equal(getAbi('0.31.0', 'electron'), '45')
  t.equal(getAbi('0.30.0', 'electron'), '44')
  t.end()
})

test('getAbi calculates correct Node-Webkit ABI', function (t) {
  t.throws(function () { getAbi(undefined, 'node-webkit') })
  t.throws(function () { getAbi(getNextTarget('node-webkit'), 'node-webkit') })
  t.equal(getAbi('0.13.0', 'node-webkit'), '47')
  t.equal(getAbi('0.14.0', 'node-webkit'), '47')
  t.equal(getAbi('0.15.0', 'node-webkit'), '48')
  t.equal(getAbi('0.16.0', 'node-webkit'), '48')
  t.equal(getAbi('0.17.0', 'node-webkit'), '48')
  t.equal(getAbi('0.18.2', 'node-webkit'), '48')
  t.equal(getAbi('0.18.3', 'node-webkit'), '51')
  t.equal(getAbi('0.19.0', 'node-webkit'), '51')
  t.equal(getAbi('0.20.0', 'node-webkit'), '51')
  t.equal(getAbi('0.21.0', 'node-webkit'), '51')
  t.equal(getAbi('0.22.0', 'node-webkit'), '51')
  t.equal(getAbi('0.23.0', 'node-webkit'), '57')
  t.equal(getAbi('0.24.0', 'node-webkit'), '57')
  t.equal(getAbi('0.25.0', 'node-webkit'), '57')
  t.equal(getAbi('0.26.4', 'node-webkit'), '57')
  t.equal(getAbi('0.26.5', 'node-webkit'), '59')
  t.end()
})

test('getAbi supports leading v', function (t) {
  t.equal(getAbi('v7.2.0'), '51')
  t.end()
})

test('getAbi returns abi if passed as target', function (t) {
  t.equal(getAbi('57'), '57')
  t.end()
})
