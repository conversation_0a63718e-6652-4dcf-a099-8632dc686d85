{"name": "multiline", "version": "1.0.2", "description": "Multiline strings in JavaScript", "license": "MIT", "repository": "sindresorhus/multiline", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "browser": "browserify -s $npm_package_name -o browser.js ."}, "files": ["index.js", "browser.js"], "keywords": ["browser", "multiline", "multi-line", "multiple", "line", "comment", "string", "str", "text", "comment"], "dependencies": {"strip-indent": "^1.0.0"}, "devDependencies": {"browserify": "^6.0.2", "callsites": "^1.0.0", "mocha": "*", "uglify-js": "^2.4.13"}}