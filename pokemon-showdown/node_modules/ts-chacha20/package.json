{"name": "ts-chacha20", "version": "1.2.0", "description": "Typescript ChaCha20 stream cipher", "main": "build/src/chacha20.js", "files": ["build"], "devDependencies": {"@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@types/jest-expect-message": "^1.0.3", "jest": "^27.4.7", "jest-expect-message": "^1.0.2", "typescript": "^4.5.5"}, "scripts": {"build": "tsc", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/hscrypt/ts-chacha20.git"}, "keywords": ["cryptography", "cipher", "encryption", "stream", "salsa", "salsa20", "chacha", "chacha20"], "author": "<PERSON><PERSON><PERSON>", "contributors": [{"name": "<PERSON>"}], "license": "MIT", "bugs": {"url": "https://github.com/hscrypt/ts-chacha20/issues"}, "homepage": "https://github.com/hscrypt/ts-chacha20#readme"}