{"name": "permessage-deflate", "description": "Per-message DEFLATE compression extension for WebSocket connections", "homepage": "https://github.com/faye/permessage-deflate-node", "author": "<PERSON> <<EMAIL>> (http://jcoglan.com/)", "keywords": ["websocket", "compression", "deflate"], "license": "Apache-2.0", "version": "0.1.7", "engines": {"node": ">=0.8.0"}, "files": ["lib"], "main": "./lib/permessage_deflate", "dependencies": {"safe-buffer": "*"}, "devDependencies": {"jstest": "*"}, "scripts": {"test": "jstest spec/runner.js"}, "repository": {"type": "git", "url": "git://github.com/faye/permessage-deflate-node.git"}, "bugs": "https://github.com/faye/permessage-deflate-node/issues"}