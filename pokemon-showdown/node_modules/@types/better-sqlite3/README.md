# Installation
> `npm install --save @types/better-sqlite3`

# Summary
This package contains type definitions for better-sqlite3 (https://github.com/JoshuaWise/better-sqlite3).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/better-sqlite3.

### Additional Details
 * Last updated: Thu, 08 Dec 2022 03:03:10 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [<PERSON>](https://github.com/Morfent), [<PERSON><PERSON>](https://github.com/matrumz), [<PERSON> Aguilar](https://github.com/sant123), [<PERSON>](https://github.com/loghorn), [<PERSON>](https://github.com/andykais), [<PERSON>](https://github.com/mrkstwrt), and [<PERSON><PERSON><PERSON>](https://github.com/stamerf).
