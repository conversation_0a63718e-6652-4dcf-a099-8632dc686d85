{"name": "@types/better-sqlite3", "version": "7.6.3", "description": "TypeScript definitions for better-sqlite3", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/better-sqlite3", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/Morfent", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/matrumz", "githubUsername": "matrumz"}, {"name": "Santiago Aguilar", "url": "https://github.com/sant123", "githubUsername": "sant123"}, {"name": "<PERSON>", "url": "https://github.com/loghorn", "githubUsername": "loghorn"}, {"name": "<PERSON>", "url": "https://github.com/andykais", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/mrkstwrt", "githubUsername": "mrkstwrt"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/stamerf", "githubUsername": "stamerf"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/better-sqlite3"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "3700307bf2f47e37325f60e83c19146e37baedb282826e15c695ebd498fa1b61", "typeScriptVersion": "4.2"}