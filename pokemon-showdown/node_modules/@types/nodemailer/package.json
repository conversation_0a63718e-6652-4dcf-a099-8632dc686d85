{"name": "@types/nodemailer", "version": "6.4.7", "description": "TypeScript definitions for Nodemailer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/nodemailer", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/rogierschouten", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/dex4er", "githubUsername": "dex4er"}, {"name": "<PERSON>", "url": "https://github.com/bioball", "githubUsername": "bioball"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/nodemailer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "cd73c4be307c49eb2d905f96230801f0250528c272be45414126ceb547d31d51", "typeScriptVersion": "4.2"}