# Installation
> `npm install --save @types/pg`

# Summary
This package contains type definitions for pg (https://github.com/brianc/node-postgres).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pg.

### Additional Details
 * Last updated: Fri, 23 Dec 2022 01:32:44 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node), [@types/pg-protocol](https://npmjs.com/package/@types/pg-protocol), [@types/pg-types](https://npmjs.com/package/@types/pg-types)
 * Global values: none

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/pspeter3), and [<PERSON>](https://github.com/HoldYourWaffle).
