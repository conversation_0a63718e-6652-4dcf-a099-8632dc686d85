{"name": "@types/pg", "version": "8.6.6", "description": "TypeScript definitions for pg", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pg", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/pspeter3", "githubUsername": "pspeter3"}, {"name": "<PERSON>", "url": "https://github.com/HoldYourWaffle", "githubUsername": "HoldYourWaffle"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pg"}, "scripts": {}, "dependencies": {"@types/node": "*", "pg-protocol": "*", "pg-types": "^2.2.0"}, "typesPublisherContentHash": "9dc91e94a7ef022f3f7b5341a63d5631915c07c4b4053f54383ff18807b3d7d8", "typeScriptVersion": "4.2"}