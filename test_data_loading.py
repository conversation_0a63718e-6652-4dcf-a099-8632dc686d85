#!/usr/bin/env python3
"""
Test script for Pokemon data loading
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from plugins.pokemon.database.data_loader import ShowdownData<PERSON>oader
from plugins.pokemon.config import config

async def test_data_loading():
    """Test the data loading functionality"""
    print("Testing Pokemon data loading...")
    
    # Initialize the data loader
    loader = ShowdownDataLoader()
    
    print(f"Showdown path: {loader.showdown_path}")
    print(f"Data path: {loader.data_path}")
    print(f"Dist path: {loader.dist_path}")
    
    # Check if paths exist
    print(f"Showdown path exists: {loader.showdown_path.exists()}")
    print(f"Data path exists: {loader.data_path.exists()}")
    print(f"Dist path exists: {loader.dist_path.exists()}")
    
    # Test loading a single file
    print("\nTesting pokedex.js loading...")
    pokedex_data = await loader._load_js_data("pokedex.js")
    
    if pokedex_data:
        print(f"Successfully loaded pokedex data with {len(pokedex_data)} entries")
        # Show first few Pokemon
        for i, (pokemon_id, data) in enumerate(pokedex_data.items()):
            if i >= 3:  # Show only first 3
                break
            print(f"  {pokemon_id}: {data.get('name', 'Unknown')} (#{data.get('num', 0)})")
    else:
        print("Failed to load pokedex data")
    
    # Test loading all data
    print("\nTesting full data loading...")
    all_data = await loader.load_all_data()
    
    if all_data:
        print("Successfully loaded all data:")
        for data_type, data_dict in all_data.items():
            print(f"  {data_type}: {len(data_dict)} entries")
    else:
        print("Failed to load all data")

if __name__ == "__main__":
    asyncio.run(test_data_loading())
