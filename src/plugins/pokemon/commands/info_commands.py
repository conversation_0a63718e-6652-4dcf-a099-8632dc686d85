"""
Info Commands

Nonebot commands for Pokemon information queries.
"""

import logging
from typing import Optional, List
from nonebot import on_command, get_driver
from nonebot.adapters.onebot.v11 import Bot, Event, MessageEvent
from nonebot.params import CommandArg
from nonebot.typing import T_State

from ..models import Pokemon, Move, Item, Ability, PokemonType
from ..database import PokemonDatabase

logger = logging.getLogger(__name__)

# Global instances
pokemon_db: Optional[PokemonDatabase] = None

# Command handlers
pokemon_info = on_command("pokemon info", aliases={"宝可梦信息", "pokemon查询"}, priority=5)
move_info = on_command("pokemon move", aliases={"技能信息", "招式信息"}, priority=5)
item_info = on_command("pokemon item", aliases={"道具信息", "物品信息"}, priority=5)
ability_info = on_command("pokemon ability", aliases={"特性信息", "能力信息"}, priority=5)
pokemon_search = on_command("pokemon search", aliases={"宝可梦搜索", "pokemon搜索"}, priority=5)


@pokemon_info.handle()
async def handle_pokemon_info(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle Pokemon info command"""
    try:
        if not pokemon_db or not pokemon_db._initialized:
            await pokemon_info.finish("数据库尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await pokemon_info.finish("请指定要查询的宝可梦名称")
        
        # Get Pokemon data
        pokemon = await pokemon_db.get_pokemon(args_str)
        if not pokemon:
            await pokemon_info.finish(f"未找到宝可梦: {args_str}")
        
        # Format Pokemon info
        info_text = _format_pokemon_info(pokemon)
        await pokemon_info.finish(info_text)
        
    except Exception as e:
        logger.error(f"Error in pokemon info: {e}")
        await pokemon_info.finish("查询宝可梦信息时出现错误")


@move_info.handle()
async def handle_move_info(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle move info command"""
    try:
        if not pokemon_db or not pokemon_db._initialized:
            await move_info.finish("数据库尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await move_info.finish("请指定要查询的技能名称")
        
        # Get move data
        move = await pokemon_db.get_move(args_str)
        if not move:
            await move_info.finish(f"未找到技能: {args_str}")
        
        # Format move info
        info_text = _format_move_info(move)
        await move_info.finish(info_text)
        
    except Exception as e:
        logger.error(f"Error in move info: {e}")
        await move_info.finish("查询技能信息时出现错误")


@item_info.handle()
async def handle_item_info(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle item info command"""
    try:
        if not pokemon_db or not pokemon_db._initialized:
            await item_info.finish("数据库尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await item_info.finish("请指定要查询的道具名称")
        
        # Get item data
        item = await pokemon_db.get_item(args_str)
        if not item:
            await item_info.finish(f"未找到道具: {args_str}")
        
        # Format item info
        info_text = _format_item_info(item)
        await item_info.finish(info_text)
        
    except Exception as e:
        logger.error(f"Error in item info: {e}")
        await item_info.finish("查询道具信息时出现错误")


@ability_info.handle()
async def handle_ability_info(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle ability info command"""
    try:
        if not pokemon_db or not pokemon_db._initialized:
            await ability_info.finish("数据库尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await ability_info.finish("请指定要查询的特性名称")
        
        # Get ability data
        ability = await pokemon_db.get_ability(args_str)
        if not ability:
            await ability_info.finish(f"未找到特性: {args_str}")
        
        # Format ability info
        info_text = _format_ability_info(ability)
        await ability_info.finish(info_text)
        
    except Exception as e:
        logger.error(f"Error in ability info: {e}")
        await ability_info.finish("查询特性信息时出现错误")


@pokemon_search.handle()
async def handle_pokemon_search(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle Pokemon search command"""
    try:
        if not pokemon_db or not pokemon_db._initialized:
            await pokemon_search.finish("数据库尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await pokemon_search.finish(
                "使用方法: /pokemon search <关键词> [类型:type] [世代:gen]\n"
                "示例: /pokemon search 皮卡\n"
                "      /pokemon search 火 type:fire\n"
                "      /pokemon search 龙 gen:1"
            )
        
        # Parse search arguments
        parts = args_str.split()
        query = parts[0]
        filters = {}
        
        for part in parts[1:]:
            if ":" in part:
                key, value = part.split(":", 1)
                if key == "type":
                    filters["type"] = value
                elif key == "gen":
                    try:
                        filters["generation"] = int(value)
                    except ValueError:
                        pass
        
        # Search Pokemon
        results = await pokemon_db.search_pokemon(query, limit=10, filters=filters)
        
        if not results:
            await pokemon_search.finish(f"未找到匹配的宝可梦: {query}")
        
        # Format results
        result_lines = [f"搜索结果 ({len(results)} 个):"]
        for pokemon in results:
            types_str = "/".join([t.value for t in pokemon.types])
            result_lines.append(f"- {pokemon.name} (#{pokemon.num:03d}) [{types_str}]")
        
        if len(results) == 10:
            result_lines.append("(仅显示前10个结果)")
        
        await pokemon_search.finish("\n".join(result_lines))
        
    except Exception as e:
        logger.error(f"Error in pokemon search: {e}")
        await pokemon_search.finish("搜索宝可梦时出现错误")


def _format_pokemon_info(pokemon: Pokemon) -> str:
    """Format Pokemon information for display"""
    lines = [
        f"🐾 {pokemon.name} (#{pokemon.num:03d})",
        f"属性: {'/'.join([t.value for t in pokemon.types])}",
        f"身高: {pokemon.height}m",
        f"体重: {pokemon.weight}kg",
        f"颜色: {pokemon.color}",
        "",
        "种族值:",
        f"  HP: {pokemon.base_stats.hp}",
        f"  攻击: {pokemon.base_stats.attack}",
        f"  防御: {pokemon.base_stats.defense}",
        f"  特攻: {pokemon.base_stats.special_attack}",
        f"  特防: {pokemon.base_stats.special_defense}",
        f"  速度: {pokemon.base_stats.speed}",
        f"  总和: {sum([
            pokemon.base_stats.hp,
            pokemon.base_stats.attack,
            pokemon.base_stats.defense,
            pokemon.base_stats.special_attack,
            pokemon.base_stats.special_defense,
            pokemon.base_stats.speed
        ])}",
        "",
        "特性:"
    ]
    
    for slot, ability in pokemon.abilities.items():
        slot_name = {"0": "特性1", "1": "特性2", "H": "隐藏特性"}.get(slot, f"特性{slot}")
        lines.append(f"  {slot_name}: {ability}")
    
    if pokemon.tier:
        lines.append(f"\n分级: {pokemon.tier}")
    
    return "\n".join(lines)


def _format_move_info(move: Move) -> str:
    """Format move information for display"""
    lines = [
        f"⚡ {move.name}",
        f"属性: {move.type.value}",
        f"分类: {move.category}",
        f"威力: {move.base_power if move.base_power > 0 else '-'}",
        f"命中: {move.accuracy if isinstance(move.accuracy, int) else '必中'}",
        f"PP: {move.pp}",
        f"优先度: {move.priority}",
        f"目标: {move.target}",
        "",
        f"描述: {move.description or move.short_description}"
    ]
    
    return "\n".join(lines)


def _format_item_info(item: Item) -> str:
    """Format item information for display"""
    lines = [
        f"🎒 {item.name}",
        f"编号: #{item.num}",
        "",
        f"描述: {item.description or item.short_description}"
    ]
    
    return "\n".join(lines)


def _format_ability_info(ability: Ability) -> str:
    """Format ability information for display"""
    lines = [
        f"✨ {ability.name}",
        f"编号: #{ability.num}",
        "",
        f"描述: {ability.description or ability.short_description}"
    ]
    
    return "\n".join(lines)


# Initialize global instances when plugin loads
@get_driver().on_startup
async def init_info_commands():
    """Initialize info commands"""
    global pokemon_db
    
    # Import from main plugin
    from .. import pokemon_db as pdb
    
    pokemon_db = pdb
