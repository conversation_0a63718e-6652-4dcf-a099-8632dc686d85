"""
Help Commands

Nonebot help commands for Pokemon plugin.
"""

from nonebot import on_command
from nonebot.adapters.onebot.v11 import Bot, Event, MessageEvent
from nonebot.params import CommandArg
from nonebot.typing import T_State

# Help command handler
pokemon_help = on_command("pokemon help", aliases={"pokemon帮助", "宝可梦帮助"}, priority=5)


@pokemon_help.handle()
async def handle_pokemon_help(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle Pokemon help command"""
    args_str = str(args).strip()
    
    if not args_str:
        # General help
        help_text = """
🐾 Pokemon Battle System 帮助

📋 基本命令:
/pokemon help - 显示此帮助信息
/pokemon help <类别> - 显示特定类别的帮助

📚 命令类别:
• battle - 对战相关命令
• team - 队伍管理命令  
• info - 信息查询命令
• admin - 管理员命令 (仅管理员)

💡 快速开始:
1. /pokemon info 皮卡丘 - 查询宝可梦信息
2. /pokemon team create 我的队伍 gen9ou - 创建队伍
3. /pokemon battle @好友 gen9ou - 发起对战

🔗 更多信息请使用 /pokemon help <类别>
        """.strip()
        
        await pokemon_help.finish(help_text)
    
    elif args_str == "battle":
        # Battle commands help
        help_text = """
⚔️ 对战命令帮助

🎯 发起对战:
/pokemon battle <@用户> <格式> - 向用户发起对战挑战
  格式: gen9ou, gen9uu, gen9randombattle 等
  示例: /pokemon battle @张三 gen9ou

✅ 接受对战:
/pokemon accept <挑战者ID> - 接受对战挑战
  示例: /pokemon accept 123456

🎮 对战操作:
/pokemon action <对战ID> <行动> - 提交对战行动
  示例: /pokemon action abc123 move 1
         /pokemon action abc123 switch 2

📊 查看状态:
/pokemon status - 查看你的对战状态
/pokemon status <对战ID> - 查看特定对战状态

🏳️ 投降:
/pokemon forfeit <对战ID> - 投降对战
        """.strip()
        
        await pokemon_help.finish(help_text)
    
    elif args_str == "team":
        # Team commands help
        help_text = """
👥 队伍管理帮助

📝 创建队伍:
/pokemon team create <队伍名> <格式> - 创建新队伍
  示例: /pokemon team create 我的队伍 gen9ou

📥 导入队伍:
/pokemon team import <队伍名> <队伍数据> - 导入宝可梦到队伍
  格式: 宝可梦1|技能1,技能2,技能3,技能4;宝可梦2|技能1,技能2,技能3,技能4
  示例: /pokemon team import 测试队伍 皮卡丘|十万伏特,电光一闪,铁尾,影分身

📤 导出队伍:
/pokemon team export <队伍名> - 导出队伍信息

✅ 验证队伍:
/pokemon team validate <队伍名> - 验证队伍是否符合格式规则

📋 队伍列表:
/pokemon team list - 查看你的所有队伍
        """.strip()
        
        await pokemon_help.finish(help_text)
    
    elif args_str == "info":
        # Info commands help
        help_text = """
📖 信息查询帮助

🐾 宝可梦信息:
/pokemon info <宝可梦名> - 查询宝可梦详细信息
  示例: /pokemon info 皮卡丘

⚡ 技能信息:
/pokemon move <技能名> - 查询技能详细信息
  示例: /pokemon move 十万伏特

🎒 道具信息:
/pokemon item <道具名> - 查询道具详细信息
  示例: /pokemon item 精灵球

✨ 特性信息:
/pokemon ability <特性名> - 查询特性详细信息
  示例: /pokemon ability 静电

🔍 搜索功能:
/pokemon search <关键词> [过滤器] - 搜索宝可梦
  过滤器: type:属性, gen:世代
  示例: /pokemon search 皮卡 type:electric
        """.strip()
        
        await pokemon_help.finish(help_text)
    
    elif args_str == "admin":
        # Admin commands help
        help_text = """
🔧 管理员命令帮助 (仅超级用户)

📊 系统状态:
/pokemon admin stats - 查看系统运行状态

🔄 刷新操作:
/pokemon admin refresh [类型] - 刷新系统组件
  类型: data (数据), cache (缓存), showdown (桥接)
  示例: /pokemon admin refresh data

🛑 系统关闭:
/pokemon admin shutdown - 关闭Pokemon系统

⚠️ 注意: 管理员命令需要超级用户权限
        """.strip()
        
        await pokemon_help.finish(help_text)
    
    else:
        await pokemon_help.finish(
            f"未知的帮助类别: {args_str}\n"
            "可用类别: battle, team, info, admin"
        )
