"""
Team Commands

Nonebot commands for Pokemon team management.
"""

import json
import logging
from typing import Optional, Dict, Any
from nonebot import on_command, get_driver
from nonebot.adapters.onebot.v11 import Bot, Event, MessageEvent
from nonebot.params import CommandArg
from nonebot.typing import T_State

from ..models import Team, PokemonSet, BattleFormat
from ..database import PokemonDatabase
from ..battle import TeamValidator

logger = logging.getLogger(__name__)

# Global instances
pokemon_db: Optional[PokemonDatabase] = None
team_validator: Optional[TeamValidator] = None

# Simple team storage (in production, use a proper database)
user_teams: Dict[str, Dict[str, Team]] = {}  # user_id -> team_name -> team

# Command handlers
team_create = on_command("pokemon team create", aliases={"创建队伍", "pokemon队伍创建"}, priority=5)
team_import = on_command("pokemon team import", aliases={"导入队伍", "pokemon队伍导入"}, priority=5)
team_export = on_command("pokemon team export", aliases={"导出队伍", "pokemon队伍导出"}, priority=5)
team_validate = on_command("pokemon team validate", aliases={"验证队伍", "pokemon队伍验证"}, priority=5)
team_list = on_command("pokemon team list", aliases={"队伍列表", "pokemon队伍列表"}, priority=5)


@team_create.handle()
async def handle_team_create(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle team create command"""
    try:
        if not pokemon_db or not pokemon_db._initialized:
            await team_create.finish("数据库尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await team_create.finish(
                "使用方法: /pokemon team create <队伍名称> <格式>\n"
                "示例: /pokemon team create 我的队伍 gen9ou"
            )
        
        parts = args_str.split(maxsplit=1)
        if len(parts) < 2:
            await team_create.finish("请指定队伍名称和格式")
        
        team_name = parts[0]
        format_str = parts[1]
        
        # Validate format
        try:
            battle_format = BattleFormat(format_str.lower())
        except ValueError:
            await team_create.finish(f"不支持的格式: {format_str}")
        
        user_id = str(event.user_id)
        
        # Check if team already exists
        if user_id in user_teams and team_name in user_teams[user_id]:
            await team_create.finish(f"队伍 '{team_name}' 已存在")
        
        # Create empty team
        team = Team(
            name=team_name,
            pokemon=[],
            format=battle_format,
            owner_id=user_id
        )
        
        # Store team
        if user_id not in user_teams:
            user_teams[user_id] = {}
        user_teams[user_id][team_name] = team
        
        await team_create.finish(
            f"已创建队伍 '{team_name}' (格式: {battle_format.value})\n"
            f"使用 '/pokemon team import {team_name} <队伍数据>' 添加宝可梦"
        )
        
    except Exception as e:
        logger.error(f"Error in team create: {e}")
        await team_create.finish("创建队伍时出现错误")


@team_import.handle()
async def handle_team_import(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle team import command"""
    try:
        if not pokemon_db or not pokemon_db._initialized:
            await team_import.finish("数据库尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await team_import.finish(
                "使用方法: /pokemon team import <队伍名称> <队伍数据>\n"
                "队伍数据格式: 宝可梦1|技能1,技能2,技能3,技能4;宝可梦2|技能1,技能2,技能3,技能4\n"
                "示例: /pokemon team import 测试队伍 皮卡丘|十万伏特,电光一闪,铁尾,影分身;妙蛙种子|藤鞭,催眠粉,光合作用,种子炸弹"
            )
        
        parts = args_str.split(maxsplit=1)
        if len(parts) < 2:
            await team_import.finish("请指定队伍名称和队伍数据")
        
        team_name = parts[0]
        team_data = parts[1]
        user_id = str(event.user_id)
        
        # Check if team exists
        if user_id not in user_teams or team_name not in user_teams[user_id]:
            await team_import.finish(f"队伍 '{team_name}' 不存在，请先创建")
        
        team = user_teams[user_id][team_name]
        
        # Parse team data
        pokemon_sets = []
        pokemon_entries = team_data.split(';')
        
        for entry in pokemon_entries:
            if '|' not in entry:
                continue
            
            pokemon_name, moves_str = entry.split('|', 1)
            pokemon_name = pokemon_name.strip()
            moves = [move.strip() for move in moves_str.split(',')]
            
            # Validate Pokemon exists
            pokemon_data = await pokemon_db.get_pokemon(pokemon_name)
            if not pokemon_data:
                await team_import.finish(f"未找到宝可梦: {pokemon_name}")
            
            # Validate moves exist
            for move_name in moves:
                move_data = await pokemon_db.get_move(move_name)
                if not move_data:
                    await team_import.finish(f"未找到技能: {move_name}")
            
            # Create Pokemon set
            pokemon_set = PokemonSet(
                name=pokemon_data.name,
                species=pokemon_data.name,
                moves=moves,
                level=50 if team.format != BattleFormat.GEN9LC else 5
            )
            pokemon_sets.append(pokemon_set)
        
        if len(pokemon_sets) == 0:
            await team_import.finish("未解析到有效的宝可梦数据")
        
        if len(pokemon_sets) > 6:
            await team_import.finish("队伍最多只能有6只宝可梦")
        
        # Update team
        team.pokemon = pokemon_sets
        
        await team_import.finish(
            f"已导入 {len(pokemon_sets)} 只宝可梦到队伍 '{team_name}'\n"
            f"使用 '/pokemon team validate {team_name}' 验证队伍"
        )
        
    except Exception as e:
        logger.error(f"Error in team import: {e}")
        await team_import.finish("导入队伍时出现错误")


@team_export.handle()
async def handle_team_export(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle team export command"""
    try:
        args_str = str(args).strip()
        if not args_str:
            await team_export.finish("请指定要导出的队伍名称")
        
        team_name = args_str
        user_id = str(event.user_id)
        
        # Check if team exists
        if user_id not in user_teams or team_name not in user_teams[user_id]:
            await team_export.finish(f"队伍 '{team_name}' 不存在")
        
        team = user_teams[user_id][team_name]
        
        if not team.pokemon:
            await team_export.finish(f"队伍 '{team_name}' 为空")
        
        # Format team data
        export_lines = [f"队伍: {team.name} (格式: {team.format.value})"]
        
        for i, pokemon in enumerate(team.pokemon, 1):
            moves_str = ", ".join(pokemon.moves)
            export_lines.append(f"{i}. {pokemon.species} | {moves_str}")
        
        await team_export.finish("\n".join(export_lines))
        
    except Exception as e:
        logger.error(f"Error in team export: {e}")
        await team_export.finish("导出队伍时出现错误")


@team_validate.handle()
async def handle_team_validate(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle team validate command"""
    try:
        if not team_validator:
            await team_validate.finish("队伍验证器尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await team_validate.finish("请指定要验证的队伍名称")
        
        team_name = args_str
        user_id = str(event.user_id)
        
        # Check if team exists
        if user_id not in user_teams or team_name not in user_teams[user_id]:
            await team_validate.finish(f"队伍 '{team_name}' 不存在")
        
        team = user_teams[user_id][team_name]
        
        if not team.pokemon:
            await team_validate.finish(f"队伍 '{team_name}' 为空，无法验证")
        
        # Validate team
        is_valid, errors = await team_validator.validate_team(team, team.format)
        
        if is_valid:
            await team_validate.finish(f"✅ 队伍 '{team_name}' 验证通过！")
        else:
            error_text = "\n".join([f"- {error}" for error in errors[:10]])
            if len(errors) > 10:
                error_text += f"\n... 还有 {len(errors) - 10} 个错误"
            
            await team_validate.finish(
                f"❌ 队伍 '{team_name}' 验证失败:\n{error_text}"
            )
        
    except Exception as e:
        logger.error(f"Error in team validate: {e}")
        await team_validate.finish("验证队伍时出现错误")


@team_list.handle()
async def handle_team_list(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle team list command"""
    try:
        user_id = str(event.user_id)
        
        if user_id not in user_teams or not user_teams[user_id]:
            await team_list.finish("你还没有创建任何队伍")
        
        teams = user_teams[user_id]
        team_lines = ["你的队伍列表:"]
        
        for team_name, team in teams.items():
            pokemon_count = len(team.pokemon)
            status = "✅" if team.is_valid else "❌" if team.validation_errors else "❓"
            team_lines.append(
                f"{status} {team_name} ({team.format.value}) - {pokemon_count}/6 宝可梦"
            )
        
        await team_list.finish("\n".join(team_lines))
        
    except Exception as e:
        logger.error(f"Error in team list: {e}")
        await team_list.finish("获取队伍列表时出现错误")


# Initialize global instances when plugin loads
@get_driver().on_startup
async def init_team_commands():
    """Initialize team commands"""
    global pokemon_db, team_validator
    
    # Import from main plugin
    from .. import pokemon_db as pdb
    from ..battle import TeamValidator
    from .. import showdown_bridge
    
    pokemon_db = pdb
    if pokemon_db and showdown_bridge:
        team_validator = TeamValidator(pokemon_db, showdown_bridge)
