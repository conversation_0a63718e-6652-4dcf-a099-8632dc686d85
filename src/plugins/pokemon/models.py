"""
Data models for Pokemon Plugin

Defines all the data structures used throughout the plugin.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any, Union, Literal
from enum import Enum
import uuid
from datetime import datetime


class BattleFormat(str, Enum):
    """Supported battle formats"""
    GEN9OU = "gen9ou"
    GEN9UU = "gen9uu"
    GEN9RU = "gen9ru"
    GEN9NU = "gen9nu"
    GEN9PU = "gen9pu"
    GEN9LC = "gen9lc"
    GEN9MONOTYPE = "gen9monotype"
    GEN9RANDOMBATTLE = "gen9randombattle"
    GEN9CUSTOMGAME = "gen9customgame"


class BattleStatus(str, Enum):
    """Battle status enumeration"""
    WAITING = "waiting"
    TEAM_PREVIEW = "team_preview"
    ACTIVE = "active"
    FINISHED = "finished"
    CANCELLED = "cancelled"
    ERROR = "error"


class PokemonStat(BaseModel):
    """Pokemon base stats"""
    hp: int
    attack: int
    defense: int
    special_attack: int
    special_defense: int
    speed: int


class PokemonType(str, Enum):
    """Pokemon types"""
    NORMAL = "Normal"
    FIRE = "Fire"
    WATER = "Water"
    ELECTRIC = "Electric"
    GRASS = "Grass"
    ICE = "Ice"
    FIGHTING = "Fighting"
    POISON = "Poison"
    GROUND = "Ground"
    FLYING = "Flying"
    PSYCHIC = "Psychic"
    BUG = "Bug"
    ROCK = "Rock"
    GHOST = "Ghost"
    DRAGON = "Dragon"
    DARK = "Dark"
    STEEL = "Steel"
    FAIRY = "Fairy"


class Pokemon(BaseModel):
    """Pokemon data model"""
    id: str
    name: str
    num: int
    types: List[PokemonType]
    base_stats: PokemonStat
    abilities: Dict[str, str]  # slot -> ability name
    height: float
    weight: float
    color: str
    egg_groups: List[str]
    tier: Optional[str] = None
    is_nonstandard: Optional[str] = None


class Move(BaseModel):
    """Move data model"""
    id: str
    name: str
    num: int
    type: PokemonType
    category: Literal["Physical", "Special", "Status"]
    base_power: int
    accuracy: Union[int, bool]  # True means always hits
    pp: int
    priority: int
    target: str
    description: str
    short_description: str
    flags: Dict[str, bool] = {}
    is_nonstandard: Optional[str] = None


class Item(BaseModel):
    """Item data model"""
    id: str
    name: str
    num: int
    description: str
    short_description: str
    is_nonstandard: Optional[str] = None


class Ability(BaseModel):
    """Ability data model"""
    id: str
    name: str
    num: int
    description: str
    short_description: str
    is_nonstandard: Optional[str] = None


class PokemonSet(BaseModel):
    """Pokemon team set (individual Pokemon in a team)"""
    name: str
    species: str
    item: Optional[str] = None
    ability: Optional[str] = None
    moves: List[str] = []
    nature: Optional[str] = None
    evs: Dict[str, int] = Field(default_factory=lambda: {
        "hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0
    })
    ivs: Dict[str, int] = Field(default_factory=lambda: {
        "hp": 31, "atk": 31, "def": 31, "spa": 31, "spd": 31, "spe": 31
    })
    level: int = 50
    gender: Optional[str] = None
    shiny: bool = False


class Team(BaseModel):
    """Pokemon team"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    pokemon: List[PokemonSet]
    format: BattleFormat
    owner_id: str
    created_at: datetime = Field(default_factory=datetime.now)
    is_valid: bool = True
    validation_errors: List[str] = []


class BattlePlayer(BaseModel):
    """Battle player information"""
    user_id: str
    username: str
    team: Team
    ready: bool = False


class Battle(BaseModel):
    """Battle instance"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    format: BattleFormat
    players: Dict[str, BattlePlayer]  # player_id -> player
    status: BattleStatus = BattleStatus.WAITING
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    finished_at: Optional[datetime] = None
    winner: Optional[str] = None
    log: List[str] = []
    current_turn: int = 0
    
    # Showdown-specific data
    showdown_battle_id: Optional[str] = None
    showdown_process_id: Optional[str] = None


class BattleRequest(BaseModel):
    """Battle action request from Showdown"""
    battle_id: str
    player_id: str
    request_type: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)


class BattleAction(BaseModel):
    """Battle action from player"""
    battle_id: str
    player_id: str
    action_type: str  # move, switch, team, etc.
    action_data: str
    timestamp: datetime = Field(default_factory=datetime.now)
