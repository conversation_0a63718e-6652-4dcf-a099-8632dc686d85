"""
Pokemon Plugin for Nonebot2

A complete Pokemon battle system plugin that bridges with Pokemon Showdown
for accurate Gen 9 battle mechanics.

Architecture:
- showdown_bridge: Interface with Pokemon Showdown simulator
- database: Pokemon data access layer
- battle: Battle system core logic
- commands: Nonebot command handlers
"""

from nonebot import require, get_driver
from nonebot.plugin import PluginMetadata
from nonebot.log import logger

#require("nonebot_plugin_localstore")

__plugin_meta__ = PluginMetadata(
    name="Pokemon Battle System",
    description="Complete Pokemon battle system with Pokemon Showdown integration",
    usage="Use /pokemon help to see available commands",
    type="application",
    homepage="https://github.com/your-repo/pokemon-plugin",
    supported_adapters={"~onebot.v11"},
)

# Import main components
from .battle import BattleManager
from .database import PokemonDatabase
from .showdown_bridge import ShowdownBridge

logger = logging.getLogger(__name__)

# Initialize global instances
battle_manager = BattleManager()
pokemon_db = PokemonDatabase()
showdown_bridge = ShowdownBridge()

# Import commands after global instances are created
from .commands import *


@get_driver().on_startup
async def init_pokemon_plugin():
    """Initialize the Pokemon plugin"""
    try:
        logger.info("Initializing Pokemon Plugin...")

        # Initialize database first
        logger.info("Initializing Pokemon database...")
        db_success = await pokemon_db.initialize()
        if not db_success:
            logger.error("Failed to initialize Pokemon database")
            return

        # Initialize Showdown bridge
        logger.info("Initializing Pokemon Showdown bridge...")
        bridge_success = await showdown_bridge.initialize()
        if not bridge_success:
            logger.error("Failed to initialize Pokemon Showdown bridge")
            return

        # Initialize battle manager
        logger.info("Initializing battle manager...")
        battle_success = await battle_manager.initialize(pokemon_db, showdown_bridge)
        if not battle_success:
            logger.error("Failed to initialize battle manager")
            return

        logger.info("Pokemon Plugin initialized successfully!")

    except Exception as e:
        logger.error(f"Error initializing Pokemon Plugin: {e}")


@get_driver().on_shutdown
async def shutdown_pokemon_plugin():
    """Shutdown the Pokemon plugin"""
    try:
        logger.info("Shutting down Pokemon Plugin...")

        # Shutdown battle manager
        if battle_manager:
            await battle_manager.shutdown()

        # Shutdown showdown bridge
        if showdown_bridge:
            await showdown_bridge.shutdown()

        logger.info("Pokemon Plugin shut down successfully")

    except Exception as e:
        logger.error(f"Error shutting down Pokemon Plugin: {e}")
