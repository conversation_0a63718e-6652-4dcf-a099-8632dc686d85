"""
Data Cache Manager

Manages caching of Pokemon data to improve performance.
"""

import asyncio
import json
import logging
import pickle
import time
from pathlib import Path
from typing import Dict, Any, Optional, Union
import hashlib

from ..config import config

logger = logging.getLogger(__name__)


class DataCacheManager:
    """Manages caching of Pokemon data"""
    
    def __init__(self, cache_dir: Optional[Path] = None):
        self.cache_dir = cache_dir or Path("data/pokemon_cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.memory_cache: Dict[str, Any] = {}
        self.cache_timestamps: Dict[str, float] = {}
        self.cache_ttl = config.data_update_interval  # Cache TTL in seconds
        
        self._lock = asyncio.Lock()
    
    async def get(self, key: str, default: Any = None) -> Any:
        """Get data from cache"""
        async with self._lock:
            # Check memory cache first
            if key in self.memory_cache:
                if self._is_cache_valid(key):
                    return self.memory_cache[key]
                else:
                    # Remove expired cache
                    self.memory_cache.pop(key, None)
                    self.cache_timestamps.pop(key, None)
            
            # Check disk cache
            cached_data = await self._load_from_disk(key)
            if cached_data is not None:
                # Load into memory cache
                self.memory_cache[key] = cached_data
                self.cache_timestamps[key] = time.time()
                return cached_data
            
            return default
    
    async def set(self, key: str, value: Any, persist: bool = True) -> bool:
        """Set data in cache"""
        async with self._lock:
            try:
                # Store in memory cache
                self.memory_cache[key] = value
                self.cache_timestamps[key] = time.time()
                
                # Optionally persist to disk
                if persist and config.cache_pokemon_data:
                    await self._save_to_disk(key, value)
                
                return True
                
            except Exception as e:
                logger.error(f"Error setting cache for key {key}: {e}")
                return False
    
    async def delete(self, key: str) -> bool:
        """Delete data from cache"""
        async with self._lock:
            try:
                # Remove from memory cache
                self.memory_cache.pop(key, None)
                self.cache_timestamps.pop(key, None)
                
                # Remove from disk cache
                await self._delete_from_disk(key)
                
                return True
                
            except Exception as e:
                logger.error(f"Error deleting cache for key {key}: {e}")
                return False
    
    async def clear(self) -> bool:
        """Clear all cache data"""
        async with self._lock:
            try:
                # Clear memory cache
                self.memory_cache.clear()
                self.cache_timestamps.clear()
                
                # Clear disk cache
                for cache_file in self.cache_dir.glob("*.cache"):
                    cache_file.unlink()
                
                logger.info("Cache cleared successfully")
                return True
                
            except Exception as e:
                logger.error(f"Error clearing cache: {e}")
                return False
    
    async def get_cache_info(self) -> Dict[str, Any]:
        """Get cache statistics"""
        async with self._lock:
            disk_files = list(self.cache_dir.glob("*.cache"))
            disk_size = sum(f.stat().st_size for f in disk_files)
            
            return {
                "memory_entries": len(self.memory_cache),
                "disk_entries": len(disk_files),
                "disk_size_bytes": disk_size,
                "cache_dir": str(self.cache_dir),
                "ttl_seconds": self.cache_ttl
            }
    
    def _is_cache_valid(self, key: str) -> bool:
        """Check if cache entry is still valid"""
        if key not in self.cache_timestamps:
            return False
        
        age = time.time() - self.cache_timestamps[key]
        return age < self.cache_ttl
    
    async def _load_from_disk(self, key: str) -> Optional[Any]:
        """Load data from disk cache"""
        try:
            cache_file = self._get_cache_file_path(key)
            
            if not cache_file.exists():
                return None
            
            # Check if file is too old
            file_age = time.time() - cache_file.stat().st_mtime
            if file_age > self.cache_ttl:
                # Remove expired file
                cache_file.unlink()
                return None
            
            # Load data
            with open(cache_file, 'rb') as f:
                data = pickle.load(f)
            
            return data
            
        except Exception as e:
            logger.warning(f"Error loading cache from disk for key {key}: {e}")
            return None
    
    async def _save_to_disk(self, key: str, value: Any) -> bool:
        """Save data to disk cache"""
        try:
            cache_file = self._get_cache_file_path(key)
            
            # Create directory if it doesn't exist
            cache_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Save data
            with open(cache_file, 'wb') as f:
                pickle.dump(value, f)
            
            return True
            
        except Exception as e:
            logger.warning(f"Error saving cache to disk for key {key}: {e}")
            return False
    
    async def _delete_from_disk(self, key: str) -> bool:
        """Delete data from disk cache"""
        try:
            cache_file = self._get_cache_file_path(key)
            
            if cache_file.exists():
                cache_file.unlink()
            
            return True
            
        except Exception as e:
            logger.warning(f"Error deleting cache from disk for key {key}: {e}")
            return False
    
    def _get_cache_file_path(self, key: str) -> Path:
        """Get the file path for a cache key"""
        # Create a safe filename from the key
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{safe_key}.cache"
    
    async def preload_common_data(self, data_loader) -> bool:
        """Preload commonly used data"""
        try:
            logger.info("Preloading common Pokemon data...")
            
            # Load and cache common Pokemon
            common_pokemon = [
                "pikachu", "charizard", "blastoise", "venusaur", "mewtwo",
                "mew", "lugia", "ho-oh", "rayquaza", "arceus", "dialga",
                "palkia", "giratina", "reshiram", "zekrom", "kyurem"
            ]
            
            pokemon_data = await self.get("pokemon_data")
            if pokemon_data:
                for pokemon_id in common_pokemon:
                    if pokemon_id in pokemon_data:
                        await self.set(f"pokemon_{pokemon_id}", pokemon_data[pokemon_id])
            
            # Load and cache common moves
            common_moves = [
                "tackle", "scratch", "thunderbolt", "flamethrower", "surf",
                "earthquake", "psychic", "hyperbeam", "solarbeam", "blizzard"
            ]
            
            moves_data = await self.get("moves_data")
            if moves_data:
                for move_id in common_moves:
                    if move_id in moves_data:
                        await self.set(f"move_{move_id}", moves_data[move_id])
            
            logger.info("Common data preloaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error preloading common data: {e}")
            return False
    
    async def cleanup_expired_cache(self) -> int:
        """Clean up expired cache entries"""
        cleaned_count = 0
        
        async with self._lock:
            try:
                # Clean memory cache
                current_time = time.time()
                expired_keys = []
                
                for key, timestamp in self.cache_timestamps.items():
                    if current_time - timestamp > self.cache_ttl:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    self.memory_cache.pop(key, None)
                    self.cache_timestamps.pop(key, None)
                    cleaned_count += 1
                
                # Clean disk cache
                for cache_file in self.cache_dir.glob("*.cache"):
                    file_age = current_time - cache_file.stat().st_mtime
                    if file_age > self.cache_ttl:
                        cache_file.unlink()
                        cleaned_count += 1
                
                if cleaned_count > 0:
                    logger.info(f"Cleaned up {cleaned_count} expired cache entries")
                
                return cleaned_count
                
            except Exception as e:
                logger.error(f"Error cleaning up cache: {e}")
                return 0
