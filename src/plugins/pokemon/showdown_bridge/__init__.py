"""
Pokemon Showdown Bridge Package

This package provides the interface between our Python plugin and the Pokemon Showdown
battle simulator. It handles process management, message passing, and log parsing.
"""

from .bridge import ShowdownBridge
from .process_manager import ShowdownProcessManager
from .message_parser import ShowdownMessageParser
from .battle_interface import ShowdownBattleInterface

__all__ = [
    "ShowdownBridge",
    "ShowdownProcessManager", 
    "ShowdownMessageParser",
    "ShowdownBattleInterface"
]
